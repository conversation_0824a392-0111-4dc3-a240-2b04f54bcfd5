#===============================================================================
# Example Usage of Automatic Bridge NPC Management System
#===============================================================================

# This file shows examples of how to use the bridge NPC system in your events.

#===============================================================================
# Example 1: Simple Bridge Control Event
#===============================================================================
=begin

Create an event with the following script:

# Toggle bridge state
if $PokemonGlobal.bridge && $PokemonGlobal.bridge > 0
  pbBridgeOff
  pbMessage("The bridge has been lowered.")
else
  pbBridgeOn(2)  # Bridge at height 2
  pbMessage("The bridge has been raised.")
end

# The system will automatically:
# - Update all OnBridge NPCs on the current map
# - Set their heights, interaction states, and walkthrough properties
# - Position their shadows correctly

=end

#===============================================================================
# Example 2: Bridge Control with Multiple Heights
#===============================================================================
=begin

Create an event that allows selecting bridge height:

choices = ["Lower Bridge", "Bridge Height 1", "Bridge Height 2", "Bridge Height 3"]
choice = pbMessage("Select bridge state:", choices, 0)

case choice
when 0  # Lower Bridge
  pbBridgeOff
  pbMessage("Bridge lowered. OnBridge NPCs are now above the bridge area.")
when 1  # Height 1
  pbBridgeOn(1)
  pbMessage("Bridge raised to height 1.")
when 2  # Height 2
  pbBridgeOn(2)
  pbMessage("Bridge raised to height 2.")
when 3  # Height 3
  pbBridgeOn(3)
  pbMessage("Bridge raised to height 3.")
end

=end

#===============================================================================
# Example 3: Conditional Bridge Access
#===============================================================================
=begin

Create an event that checks for a specific item or badge:

if pbHasItem?(:SURFBOARD) || $player.badge_count >= 3
  if $PokemonGlobal.bridge && $PokemonGlobal.bridge > 0
    pbBridgeOff
    pbMessage("You lower the bridge.")
  else
    pbBridgeOn(2)
    pbMessage("You raise the bridge.")
  end
else
  pbMessage("You need a Surfboard or at least 3 badges to operate this bridge.")
end

=end

#===============================================================================
# Example 4: NPC Names for Automatic Management
#===============================================================================
=begin

Name your NPCs with "OnBridge" anywhere in the name for automatic management:

✓ Good Names:
- "Trainer OnBridge"
- "OnBridge Guard"
- "Fisherman OnBridge 1"
- "OnBridge Sailor Bob"
- "Bridge Guard OnBridge"

✗ Names that won't work:
- "Bridge Guard" (missing "OnBridge")
- "Guard on Bridge" (missing "OnBridge" - needs to be one word)
- "Trainer" (no bridge reference)

=end

#===============================================================================
# Example 5: Manual NPC Management (if needed)
#===============================================================================
=begin

If you need to manually control specific NPCs, you can use:

# Get a specific event by ID
event = $game_map.events[5]  # Event ID 5

# Check if it's an OnBridge NPC
if AutoBridgeNPC.is_onbridge_npc?(event)
  # Manually update this specific NPC
  AutoBridgeNPC.update_onbridge_npc(event)
end

# Or refresh all OnBridge NPCs manually
AutoBridgeNPC.refresh_all_bridge_npcs

=end

#===============================================================================
# Example 6: Checking Bridge State in Events
#===============================================================================
=begin

You can check the current bridge state in your events:

if AutoBridgeNPC.bridge_active?
  pbMessage("The bridge is currently raised.")
  height = AutoBridgeNPC.bridge_height
  pbMessage("Bridge height: #{height}")
else
  pbMessage("The bridge is currently lowered.")
end

=end

#===============================================================================
# Example 7: Advanced Bridge Puzzle
#===============================================================================
=begin

Create a puzzle where players must activate multiple switches to raise the bridge:

# Check if all switches are activated
switch1 = $game_switches[10]  # Switch A
switch2 = $game_switches[11]  # Switch B  
switch3 = $game_switches[12]  # Switch C

if switch1 && switch2 && switch3
  if !AutoBridgeNPC.bridge_active?
    pbBridgeOn(2)
    pbMessage("All switches activated! The bridge rises!")
    pbMessage("The OnBridge NPCs are now at bridge level.")
  else
    pbMessage("The bridge is already raised.")
  end
else
  if AutoBridgeNPC.bridge_active?
    pbBridgeOff
    pbMessage("Not all switches are active. The bridge lowers.")
    pbMessage("The OnBridge NPCs move above the bridge area.")
  end
  
  missing = []
  missing << "Switch A" if !switch1
  missing << "Switch B" if !switch2
  missing << "Switch C" if !switch3
  
  pbMessage("Missing switches: #{missing.join(', ')}")
end

=end
