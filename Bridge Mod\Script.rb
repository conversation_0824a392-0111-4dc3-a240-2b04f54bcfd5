#===============================================================================
# Automatic Bridge NPC Management System for Pokemon Essentials
# Version: 1.0.0
# Credits: FohlenETech
#
# This script automatically manages NPCs with "OnBridge" in their name to:
# 1. Appear ABOVE the bridge when pbBridgeOff is active (with shadow, walkthrough, no interaction)
# 2. Appear at player level when pbBridgeOn is active (normal shadow, solid, interactable)
# 3. Automatically set z-ordering based on bridge state
#
# NAMING CONVENTION:
# - NPCs with "OnBridge" in their name will be automatically managed
#
# BEHAVIOR:
# When bridge is OFF (pbBridgeOff):
# - OnBridge NPCs: Positioned above where bridge would be, walkthrough, no interaction
#
# When bridge is ON (pbBridgeOn):
# - OnBridge NPCs: Positioned at bridge level, solid, interactable
#===============================================================================

module AutoBridgeNPC
  # Constants for positioning and behavior
  BRIDGE_HEIGHT_MULTIPLIER = 32  # Pixels per bridge height level
  BASE_BRIDGE_OFFSET = 32        # Base offset for bridge positioning
  ABOVE_BRIDGE_OFFSET = 16       # Additional offset to appear above bridge when off

  # Cache for performance
  @@last_bridge_state = nil
  @@last_bridge_height = nil
  @@cached_bridge_npcs = []
  @@cache_dirty = true

  #=============================================================================
  # Core bridge state detection
  #=============================================================================

  # Check if bridge is currently active
  def self.bridge_active?
    return false if !$PokemonGlobal || !$PokemonGlobal.respond_to?(:bridge)
    return $PokemonGlobal.bridge && $PokemonGlobal.bridge > 0
  end

  # Get current bridge height
  def self.bridge_height
    return 0 if !$PokemonGlobal || !$PokemonGlobal.respond_to?(:bridge)
    return $PokemonGlobal.bridge || 0
  end

  #=============================================================================
  # NPC identification
  #=============================================================================

  # Check if an event is an OnBridge NPC
  def self.is_onbridge_npc?(event)
    return false if !event || !event.respond_to?(:name)
    return event.name.downcase.include?("onbridge")
  end

  # Get all OnBridge NPCs on current map
  def self.get_onbridge_npcs
    return [] if !$game_map || !$game_map.events

    # Use cache if bridge state hasn't changed
    if !bridge_state_changed? && !@@cache_dirty
      return @@cached_bridge_npcs
    end

    # Rebuild cache
    @@cached_bridge_npcs = []
    $game_map.events.each_value do |event|
      next if !event || event.erased?
      @@cached_bridge_npcs << event if is_onbridge_npc?(event)
    end

    @@cache_dirty = false
    return @@cached_bridge_npcs
  end

  # Check if bridge state has changed since last check
  def self.bridge_state_changed?
    current_state = bridge_active?
    current_height = bridge_height

    if @@last_bridge_state != current_state || @@last_bridge_height != current_height
      @@last_bridge_state = current_state
      @@last_bridge_height = current_height
      return true
    end

    return false
  end

  # Clear cache (call when changing maps)
  def self.clear_cache
    @@cached_bridge_npcs = []
    @@cache_dirty = true
    @@last_bridge_state = nil
    @@last_bridge_height = nil
  end

  #=============================================================================
  # Height and positioning calculations
  #=============================================================================

  # Calculate z-offset for OnBridge NPCs
  def self.calculate_bridge_z_offset(bridge_height, bridge_active)
    if bridge_active
      # Bridge is on - NPC at bridge level
      return (bridge_height * BRIDGE_HEIGHT_MULTIPLIER) + BASE_BRIDGE_OFFSET
    else
      # Bridge is off - NPC above where bridge would be
      return (bridge_height * BRIDGE_HEIGHT_MULTIPLIER) + BASE_BRIDGE_OFFSET + ABOVE_BRIDGE_OFFSET
    end
  end

  #=============================================================================
  # NPC behavior management
  #=============================================================================

  # Update a single OnBridge NPC based on bridge state
  def self.update_onbridge_npc(event)
    return if !event || !is_onbridge_npc?(event)
    return if !$PokemonGlobal || !$PokemonGlobal.respond_to?(:bridge)

    bridge_active = bridge_active?
    current_bridge_height = bridge_height
    bridge_z = calculate_bridge_z_offset(current_bridge_height, bridge_active)

    if bridge_active
      # Bridge is ON - NPC is at bridge level, interactable, solid
      event.through = false
      event.instance_variable_set(:@bridge_z_offset, bridge_z)
      event.instance_variable_set(:@bridge_disabled, false)
      event.instance_variable_set(:@auto_bridge_type, :onbridge_active)
    else
      # Bridge is OFF - NPC is above bridge level, non-interactable, walkthrough
      event.through = true
      event.instance_variable_set(:@bridge_z_offset, bridge_z)
      event.instance_variable_set(:@bridge_disabled, true)
      event.instance_variable_set(:@auto_bridge_type, :onbridge_inactive)
    end
  end

  # Refresh all OnBridge NPCs on current map
  def self.refresh_all_bridge_npcs
    return if !$game_map || !$game_map.events

    get_onbridge_npcs.each do |event|
      update_onbridge_npc(event)
    end
  end
end

#===============================================================================
# Game_Event extensions for bridge NPC behavior
#===============================================================================

class Game_Event
  # Override z calculation to include bridge z-offset
  alias __auto_bridge_npc_z z unless method_defined?(:__auto_bridge_npc_z)
  def z
    base_z = __auto_bridge_npc_z

    # Apply bridge z-offset if this event has one
    if @bridge_z_offset && @bridge_z_offset != 0
      final_z = base_z + @bridge_z_offset

      # Special handling for OnBridge NPCs when bridge is off
      if @auto_bridge_type == :onbridge_inactive
        # Ensure they appear above everything when bridge is off
        final_z += 100  # Additional offset to ensure visibility above bridge tiles
      end

      return final_z
    end

    return base_z
  end

  # Override start to prevent interaction when bridge disabled
  alias __auto_bridge_npc_start start unless method_defined?(:__auto_bridge_npc_start)
  def start
    # Check if this is a bridge-disabled OnBridge NPC
    if @bridge_disabled && AutoBridgeNPC.is_onbridge_npc?(self)
      return  # Don't start the event if bridge disabled
    end

    __auto_bridge_npc_start
  end
end

#===============================================================================
# Integration with existing bridge functions
#===============================================================================

# Override pbBridgeOn to automatically update OnBridge NPCs
def pbBridgeOn(height = 2)
  $PokemonGlobal.bridge = height
  AutoBridgeNPC.refresh_all_bridge_npcs
end

# Override pbBridgeOff to automatically update OnBridge NPCs
def pbBridgeOff
  $PokemonGlobal.bridge = 0
  AutoBridgeNPC.refresh_all_bridge_npcs
end

#===============================================================================
# Integration with map events
#===============================================================================

# Refresh OnBridge NPCs when map is set up
EventHandlers.add(:on_game_map_setup, :auto_bridge_npc_setup,
  proc { |map_id|
    AutoBridgeNPC.clear_cache
    AutoBridgeNPC.refresh_all_bridge_npcs
  }
)

# Refresh OnBridge NPCs when entering a new map
EventHandlers.add(:on_leave_map, :auto_bridge_npc_cleanup,
  proc { |new_map_id, new_x, new_y|
    AutoBridgeNPC.clear_cache
  }
)

#===============================================================================
# USAGE:
#===============================================================================
=begin

Simply name your NPCs with "OnBridge" anywhere in the name, for example:
- "Trainer OnBridge"
- "OnBridge Guard"
- "Fisherman OnBridge 1"

The script will automatically:

WHEN BRIDGE IS OFF (pbBridgeOff):
- Position the NPC above where the bridge would be
- Make the NPC walkthrough (through = true)
- Disable interaction (@bridge_disabled = true)
- Show shadow at elevated position

WHEN BRIDGE IS ON (pbBridgeOn with height):
- Position the NPC at bridge level
- Make the NPC solid (through = false)
- Enable interaction (@bridge_disabled = false)
- Show shadow at bridge level

Script Calls:
pbBridgeOn(2)    # Activates bridge at height 2, updates OnBridge NPCs
pbBridgeOff      # Deactivates bridge, updates OnBridge NPCs

The system handles all NPC state changes automatically!

=end