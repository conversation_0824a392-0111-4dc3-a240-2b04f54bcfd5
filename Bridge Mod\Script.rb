#===============================================================================
# Bridge-Aware NPC Management System for Pokemon Essentials
# Version: 1.0.1
# Credits: FohlenETech, WolfPP, KleinStudio
#
# This script automatically manages NPC behavior and rendering based on bridge
# states, using naming conventions to identify bridge-aware NPCs.
#
# NAMING CONVENTIONS:
# - NPCs with "OnZ" or "z100" in their name: On-bridge NPCs
# - NPCs with "OffZ" or "z0" in their name: Under-bridge NPCs
#
# BEHAVIOR:
# When bridge is ON (pbBridgeOn):
# - On-bridge NPCs: Interactable, solid, elevated by bridge height
# - Under-bridge NPCs: Interactable, solid, positioned below bridge surface
#
# When bridge is OFF (pbBridgeOff):
# - On-bridge NPCs: Non-interactable, walkthrough, floating high above
# - Under-bridge NPCs: Interactable, solid, normal ground level
#===============================================================================

module BridgeAwareNPCs
  # Constants for z-level adjustments
  BRIDGE_HEIGHT_MULTIPLIER = 32  # Pixels per bridge height level (standard RPG Maker tile height)
  BASE_BRIDGE_OFFSET = 32        # Base offset for bridge positioning
  UNDER_BRIDGE_OFFSET = -16      # Offset for under-bridge positioning (visible but below)

  # Environmental effects constants
  GRASS_OVERLAY_Z_OFFSET = 32   # Additional z-offset for grass overlays on bridge NPCs
  WATER_EFFECT_Z_OFFSET = 16    # Additional z-offset for water effects on bridge NPCs

  # Performance optimization - cache bridge NPCs and state
  @@last_bridge_state = nil
  @@last_bridge_height = nil
  @@cached_bridge_npcs = []
  @@cache_dirty = true

  #=============================================================================
  # Core bridge state detection
  #=============================================================================

  # Check if bridge is currently active
  def self.bridge_active?
    return false if !$PokemonGlobal || !$PokemonGlobal.respond_to?(:bridge)
    return $PokemonGlobal.bridge && $PokemonGlobal.bridge > 0
  end

  # Get current bridge height
  def self.bridge_height
    return 0 if !$PokemonGlobal || !$PokemonGlobal.respond_to?(:bridge)
    return $PokemonGlobal.bridge || 0
  end

  #=============================================================================
  # NPC identification system
  #=============================================================================

  # Check if an event is bridge-aware (has naming convention)
  def self.is_bridge_aware?(event)
    return false if !event || !event.respond_to?(:name) || !event.name
    name = event.name.downcase
    return name.include?("onz") || name.include?("offz") ||
           name.include?("z100") || name.include?("z0")
  end

  # Check if NPC should be on the bridge
  def self.is_on_bridge_npc?(event)
    return false if !event || !event.respond_to?(:name) || !event.name
    name = event.name.downcase
    return name.include?("onz") || name.include?("z100")
  end

  # Check if NPC should be under the bridge
  def self.is_under_bridge_npc?(event)
    return false if !event || !event.respond_to?(:name) || !event.name
    name = event.name.downcase
    return name.include?("offz") || name.include?("z0")
  end

  #=============================================================================
  # Z-ordering and positioning calculations
  #=============================================================================

  # Calculate z-offset for bridge NPCs based on bridge state and type
  def self.calculate_bridge_z_offset(bridge_height, is_on_bridge, is_active)
    if is_on_bridge
      if is_active
        # On-bridge NPCs: elevated by bridge height + 1 additional level for proper layering
        return (bridge_height + 1) * BRIDGE_HEIGHT_MULTIPLIER + BASE_BRIDGE_OFFSET
      else
        # Bridge is off: on-bridge NPCs float high above and non-interactable
        return (bridge_height + 3) * BRIDGE_HEIGHT_MULTIPLIER + BASE_BRIDGE_OFFSET
      end
    else
      if is_active
        # Under-bridge NPCs: positioned significantly below bridge surface
        # Use negative offset proportional to bridge height to ensure they're clearly under
        return -(bridge_height * BRIDGE_HEIGHT_MULTIPLIER + BASE_BRIDGE_OFFSET)
      else
        # Bridge is off: under-bridge NPCs at normal level
        return 0
      end
    end
  end

  # Get environmental z-offset for effects like grass, water
  def self.get_environmental_z_offset(event)
    return 0 if !event || !event.respond_to?(:terrain_tag)

    offset = 0
    terrain = event.terrain_tag
    bridge_type = event.instance_variable_get(:@bridge_type) || :normal

    # Grass overlay adjustments
    if terrain.respond_to?(:shows_grass_rustle) && terrain.shows_grass_rustle
      if bridge_type == :on_bridge_active || bridge_type == :on_bridge_inactive
        offset += GRASS_OVERLAY_Z_OFFSET
      end
    end

    # Water effect adjustments
    if terrain.respond_to?(:can_surf) && terrain.can_surf
      if bridge_type == :under_bridge_active
        offset -= WATER_EFFECT_Z_OFFSET
      end
    end

    return offset
  end

  #=============================================================================
  # NPC behavior management
  #=============================================================================

  # Update NPC properties based on bridge state
  def self.update_npc_for_bridge(event)
    return if !event || !event.respond_to?(:name)
    return if !is_bridge_aware?(event)
    return if !$PokemonGlobal || !$PokemonGlobal.respond_to?(:bridge)
    return if !$game_map || !$game_map.events || !$game_map.events[event.id]

    bridge_active = bridge_active?
    current_bridge_height = bridge_height
    env_z_offset = get_environmental_z_offset(event)

    if is_on_bridge_npc?(event)
      # NPC that should be on the bridge
      bridge_z = calculate_bridge_z_offset(current_bridge_height, true, bridge_active)

      if bridge_active
        # Bridge is on - NPC is interactable at bridge level
        event.through = false
        event.instance_variable_set(:@bridge_z_offset, bridge_z + env_z_offset)
        event.instance_variable_set(:@bridge_disabled, false)
        event.instance_variable_set(:@bridge_type, :on_bridge_active)
      else
        # Bridge is off - NPC is high above and non-interactable
        event.through = true
        event.instance_variable_set(:@bridge_z_offset, bridge_z + env_z_offset)
        event.instance_variable_set(:@bridge_disabled, true)
        event.instance_variable_set(:@bridge_type, :on_bridge_inactive)
      end
    elsif is_under_bridge_npc?(event)
      # NPC that should be under the bridge
      bridge_z = calculate_bridge_z_offset(current_bridge_height, false, bridge_active)

      if bridge_active
        # Bridge is on - NPC is below bridge and INTERACTABLE (player can walk under bridge to talk)
        event.through = false
        event.instance_variable_set(:@bridge_z_offset, bridge_z + env_z_offset)
        event.instance_variable_set(:@bridge_disabled, false)
        event.instance_variable_set(:@bridge_type, :under_bridge_active)
      else
        # Bridge is off - NPC is at normal level and interactable
        event.through = false
        event.instance_variable_set(:@bridge_z_offset, bridge_z + env_z_offset)
        event.instance_variable_set(:@bridge_disabled, false)
        event.instance_variable_set(:@bridge_type, :under_bridge_inactive)
      end
    end
  end

  #=============================================================================
  # Performance optimization and caching
  #=============================================================================

  # Check if bridge state has changed since last update
  def self.bridge_state_changed?
    current_state = bridge_active?
    current_height = bridge_height

    if @@last_bridge_state != current_state || @@last_bridge_height != current_height
      @@last_bridge_state = current_state
      @@last_bridge_height = current_height
      return true
    end
    return false
  end

  # Get all bridge-aware NPCs on current map (cached)
  def self.get_bridge_npcs
    if @@cache_dirty || !$game_map || $game_map.events.nil?
      @@cached_bridge_npcs.clear
      if $game_map && $game_map.events
        $game_map.events.each_value do |event|
          @@cached_bridge_npcs << event if is_bridge_aware?(event)
        end
      end
      @@cache_dirty = false
    end
    return @@cached_bridge_npcs
  end

  # Refresh all bridge-aware NPCs on the current map
  def self.refresh_all_npcs
    return if !$game_map || !$game_map.events

    # Only refresh if bridge state actually changed
    return if !bridge_state_changed?

    get_bridge_npcs.each do |event|
      update_npc_for_bridge(event)
      # Force sprite refresh if the event has a sprite
      if $scene.respond_to?(:spriteset) && $scene.spriteset($game_map.map_id)
        spriteset = $scene.spriteset($game_map.map_id)
        if spriteset.respond_to?(:character_sprites)
          sprite = spriteset.character_sprites.find { |s| s.character == event }
          sprite&.update if sprite
        end
      end
    end
  end

  # Check if environmental effects should be preserved for this character
  def self.should_preserve_effects?(character)
    return false if !character || !character.respond_to?(:bridge_type)
    bridge_type = character.bridge_type
    return bridge_type != :normal
  end

  # Mark cache as dirty (call when map changes)
  def self.invalidate_cache
    @@cache_dirty = true
    @@cached_bridge_npcs.clear
  end

  # Get bridge NPCs for debugging
  def self.get_bridge_npcs_debug
    return @@cached_bridge_npcs.dup
  end
end

#===============================================================================
# Override pbBridgeOn to refresh NPCs
#===============================================================================
if defined?(pbBridgeOn)
  alias __enhanced_bridge_npcs_pbBridgeOn pbBridgeOn
  def pbBridgeOn(height = 2)
    __enhanced_bridge_npcs_pbBridgeOn(height)
    BridgeAwareNPCs.refresh_all_npcs
  end
end

#===============================================================================
# Override pbBridgeOff to refresh NPCs
#===============================================================================
if defined?(pbBridgeOff)
  alias __enhanced_bridge_npcs_pbBridgeOff pbBridgeOff
  def pbBridgeOff
    __enhanced_bridge_npcs_pbBridgeOff
    BridgeAwareNPCs.refresh_all_npcs
  end
end

#===============================================================================
# Extend Game_Event to handle bridge-aware z-levels and interactions
#===============================================================================
class Game_Event
  # Override refresh to update bridge behavior
  alias __enhanced_bridge_npcs_refresh refresh
  def refresh
    __enhanced_bridge_npcs_refresh
    BridgeAwareNPCs.update_npc_for_bridge(self)
  end

  # Override start to prevent interaction when bridge-disabled
  alias __enhanced_bridge_npcs_start start
  def start
    # Don't start if this NPC is bridge-disabled
    return if @bridge_disabled
    __enhanced_bridge_npcs_start
  end

  # Add method to check bridge type for advanced logic
  def bridge_type
    return @bridge_type || :normal
  end

  # Add method to check if NPC is currently bridge-disabled
  def bridge_disabled?
    return @bridge_disabled || false
  end

  # Add method to get bridge z-offset
  def bridge_z_offset
    return @bridge_z_offset || 0
  end
end

#===============================================================================
# Extend Game_Character to handle proper z-ordering for bridge NPCs and player
#===============================================================================
class Game_Character
  alias __enhanced_bridge_npcs_screen_z screen_z
  def screen_z(height = 0)
    base_z = __enhanced_bridge_npcs_screen_z(height)

    # Apply bridge z-offset if this character has one
    if @bridge_z_offset && @bridge_z_offset != 0
      final_z = base_z + @bridge_z_offset

      # Special handling for under-bridge NPCs to ensure they stay below bridge
      if @bridge_type == :under_bridge_active && BridgeAwareNPCs.bridge_active?
        # Calculate a z-value that's definitely below the bridge but still visible
        bridge_height = BridgeAwareNPCs.bridge_height
        # Use the base z but subtract enough to be clearly under the bridge
        under_bridge_z = base_z - (bridge_height * BridgeAwareNPCs::BRIDGE_HEIGHT_MULTIPLIER) - BridgeAwareNPCs::BASE_BRIDGE_OFFSET
        # Ensure it's not negative (which could cause rendering issues)
        under_bridge_z = [under_bridge_z, 1].max
        return under_bridge_z
      end

      # Additional adjustments for on-bridge NPCs
      if @bridge_type == :on_bridge_active && BridgeAwareNPCs.bridge_active?
        # On-bridge NPCs should render above player when player is also on bridge
        if $game_player && $game_player.terrain_tag.respond_to?(:bridge) && $game_player.terrain_tag.bridge
          final_z += 1  # Slightly above player
        end
      end

      return final_z
    end

    # Special handling for player when bridge is active
    if self.is_a?(Game_Player) && BridgeAwareNPCs.bridge_active?
      # Check if player is on a bridge tile
      if terrain_tag.respond_to?(:bridge) && terrain_tag.bridge
        bridge_height = BridgeAwareNPCs.bridge_height
        player_bridge_z = bridge_height * BridgeAwareNPCs::BRIDGE_HEIGHT_MULTIPLIER + BridgeAwareNPCs::BASE_BRIDGE_OFFSET
        return base_z + player_bridge_z
      end
    end

    return base_z
  end
end

#===============================================================================
# Extend Sprite_Character to handle environmental effects for bridge NPCs
#===============================================================================
class Sprite_Character
  alias __enhanced_bridge_npcs_update update
  def update
    __enhanced_bridge_npcs_update
    handle_bridge_environmental_effects if @character
  end

  private

  def handle_bridge_environmental_effects
    return if !BridgeAwareNPCs.should_preserve_effects?(@character)

    # Adjust z-ordering for environmental overlays
    bridge_type = @character.bridge_type
    terrain = @character.terrain_tag

    # Ensure grass overlays render correctly
    if terrain.respond_to?(:shows_grass_rustle) && terrain.shows_grass_rustle &&
       (bridge_type == :on_bridge_active || bridge_type == :on_bridge_inactive)
      # Grass effects should render above the NPC when on bridge
      self.z += BridgeAwareNPCs::GRASS_OVERLAY_Z_OFFSET
    end

    # Ensure water effects render correctly
    if terrain.respond_to?(:can_surf) && terrain.can_surf && bridge_type == :under_bridge_active
      # Water effects should render below bridge level
      self.z -= BridgeAwareNPCs::WATER_EFFECT_Z_OFFSET
    end
  end
end

#===============================================================================
# Map transfer handling - invalidate cache when changing maps
#===============================================================================
EventHandlers.add(:on_enter_map, :bridge_aware_npcs_cache_invalidation,
  proc { |old_map_id, new_map_id|
    BridgeAwareNPCs.invalidate_cache
  }
)

#===============================================================================
# Debug methods (optional - can be removed in production)
#===============================================================================
if $DEBUG
  def debug_bridge_npcs
    npcs = BridgeAwareNPCs.get_bridge_npcs_debug
    puts "Bridge-aware NPCs on current map:"
    npcs.each do |npc|
      puts "  #{npc.name} (ID: #{npc.id}) - Type: #{npc.bridge_type}, Disabled: #{npc.bridge_disabled?}"
    end
    puts "Bridge active: #{BridgeAwareNPCs.bridge_active?}, Height: #{BridgeAwareNPCs.bridge_height}"
  end
end

#===============================================================================
# USAGE DOCUMENTATION
#===============================================================================
=begin

BRIDGE-AWARE NPC SYSTEM USAGE GUIDE
====================================

SETUP:
------
1. Name your NPCs using the following conventions:
   - On-bridge NPCs: Include "OnZ" or "z100" in the event name
     Examples: "Fisherman OnZ", "Guard z100", "Trainer OnZ Fisher"

   - Under-bridge NPCs: Include "OffZ" or "z0" in the event name
     Examples: "Kid OffZ", "Merchant z0", "NPC OffZ Walker"

2. The script automatically detects these NPCs and manages their behavior.

BEHAVIOR:
---------

WHEN BRIDGE IS ON (pbBridgeOn with height):
- On-bridge NPCs: Interactable, solid, elevated by (height + 1) levels
- Under-bridge NPCs: Non-interactable, walkthrough, positioned below bridge surface
- Player: Elevated by bridge height when on bridge tiles

WHEN BRIDGE IS OFF (pbBridgeOff):
- On-bridge NPCs: Non-interactable, walkthrough, floating high above
- Under-bridge NPCs: Interactable, solid, normal ground level

Z-LEVEL CALCULATION:
- Bridge height multiplier: 32 pixels per level (standard tile height)
- On-bridge active: (bridge_height + 1) × 32 + 32 base offset
- On-bridge inactive: (bridge_height + 3) × 32 + 32 base offset
- Under-bridge active: -16 pixels (below surface but visible)
- Under-bridge inactive: 0 pixels (ground level)

ENVIRONMENTAL EFFECTS:
- Grass overlays: Automatically adjusted for on-bridge NPCs (+32 z-offset)
- Water effects: Automatically adjusted for under-bridge NPCs (-16 z-offset)
- All terrain-based effects are preserved and properly layered

INTEGRATION:
- Automatically hooks into existing pbBridgeOn/pbBridgeOff functions
- No additional setup required beyond NPC naming
- Compatible with existing event systems and NPC interactions
- Performance optimized with caching and state change detection

DEBUGGING:
- In debug mode, use debug_bridge_npcs() in the console to see all bridge NPCs
- Check individual NPC states with npc.bridge_type and npc.bridge_disabled?

EXAMPLES:
---------

Event Names:
- "Sailor OnZ" - Will be on the bridge when active
- "Child OffZ" - Will be under the bridge when active
- "Guard z100" - Alternative on-bridge naming
- "Vendor z0" - Alternative under-bridge naming

Script Calls:
pbBridgeOn(2)    # Activates bridge at height 2, updates all NPCs
pbBridgeOff      # Deactivates bridge, updates all NPCs

The system handles all NPC state changes automatically!

=end