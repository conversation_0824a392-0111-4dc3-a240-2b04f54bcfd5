# Automatic Bridge NPC Management System

This script automatically manages NPCs with "OnBridge" in their name to properly handle bridge states in Pokemon Essentials.

## Features

1. **Automatic Height Management**: NPCs appear above the bridge when `pbBridgeOff` is active, and at player level when `pbBridgeOn` is active.

2. **Automatic Interaction Control**: NPCs become non-interactable and walkthrough when the bridge is off, and solid/interactable when the bridge is on.

3. **Automatic Z-ordering**: NPCs are automatically positioned with correct z-levels based on bridge state.

4. **Shadow Management**: Shadows are automatically positioned at the correct height based on bridge state.

## Usage

### Naming Convention

Simply name your NPCs with "OnBridge" anywhere in the name:
- `"Trainer OnBridge"`
- `"OnBridge Guard"`
- `"Fisherman OnBridge 1"`
- `"OnBridge Sailor"`

### Script Calls

Use the normal bridge functions - the system handles everything automatically:

```ruby
pbBridgeOn(2)    # Activates bridge at height 2, updates OnBridge NPCs
pbBridgeOff      # Deactivates bridge, updates OnBridge NPCs
```

### Behavior

**When Bridge is OFF (`pbBridgeOff`):**
- OnBridge NPCs are positioned above where the bridge would be
- NPCs become walkthrough (`through = true`)
- NPCs become non-interactable
- Shadows appear at elevated position

**When Bridge is ON (`pbBridgeOn` with height):**
- OnBridge NPCs are positioned at bridge level
- NPCs become solid (`through = false`)
- NPCs become interactable
- Shadows appear at bridge level

## Technical Details

### Constants (Adjustable)

```ruby
BRIDGE_HEIGHT_MULTIPLIER = 32  # Pixels per bridge height level
BASE_BRIDGE_OFFSET = 32        # Base offset for bridge positioning
ABOVE_BRIDGE_OFFSET = 16       # Additional offset to appear above bridge when off
```

### Performance

The system uses caching to minimize performance impact:
- NPCs are cached per map
- Cache is automatically cleared when changing maps
- Bridge state changes trigger cache refresh

## Installation

1. Place the `Script.rb` file in your `Bridge Mod` folder
2. The script will automatically integrate with existing `pbBridgeOn` and `pbBridgeOff` functions
3. Name your bridge NPCs with "OnBridge" in the name
4. The system works automatically!

## Compatibility

- Compatible with existing Pokemon Essentials bridge systems
- Works with custom shadow systems
- Preserves existing NPC behaviors for non-bridge NPCs
- Uses safe aliasing to avoid conflicts with other scripts

## Credits

Created by FohlenETech for Pokemon Essentials RPG Maker XP projects.
